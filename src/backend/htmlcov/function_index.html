<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">43%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-03 22:36 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee___init___py.html">app/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee_api_py.html">app/api/v1/api.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee_api_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d___init___py.html">app/api/v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t23">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t23"><data value='basic_health_check'>basic_health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t44">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t44"><data value='detailed_health_check'>detailed_health_check</data></a></td>
                <td>32</td>
                <td>32</td>
                <td>0</td>
                <td class="right" data-ratio="0 32">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t139">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t139"><data value='readiness_check'>readiness_check</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t179">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html#t179"><data value='liveness_check'>liveness_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>18</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="18 18">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t25">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t25"><data value='get_system_info'>get_system_info</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t56">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t56"><data value='get_system_metrics'>get_system_metrics</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t121">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t121"><data value='get_config_info'>get_config_info</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t186">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t186"><data value='get_environment_info'>get_environment_info</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t231">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html#t231"><data value='get_dependencies_info'>get_dependencies_info</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t127">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t127"><data value='validate_environment'>Settings.validate_environment</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t135">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t135"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t143">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t143"><data value='parse_cors_origins'>Settings.parse_cors_origins</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t150">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t150"><data value='parse_allowed_hosts'>Settings.parse_allowed_hosts</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t157">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t157"><data value='is_development'>Settings.is_development</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t162">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t162"><data value='is_production'>Settings.is_production</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t167">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t167"><data value='is_testing'>Settings.is_testing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t179">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t179"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>68</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="68 68">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t43">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t43"><data value='init__'>DatabaseManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t48">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t48"><data value='initialize'>DatabaseManager.initialize</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t88">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t88"><data value='close'>DatabaseManager.close</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t112">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t112"><data value='get_session'>DatabaseManager.get_session</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t134">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t134"><data value='health_check'>DatabaseManager.health_check</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t161">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t161"><data value='execute_raw_sql'>DatabaseManager.execute_raw_sql</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t185">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t185"><data value='engine'>DatabaseManager.engine</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t190">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t190"><data value='is_initialized'>DatabaseManager.is_initialized</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t199">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t199"><data value='get_db'>get_db</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t212">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t212"><data value='create_tables'>create_tables</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t234">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t234"><data value='drop_tables'>drop_tables</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t257">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t257"><data value='get_test_db'>get_test_db</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t23">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t23"><data value='init__'>BaseCustomException.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t42">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t42"><data value='init__'>ValidationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t60">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t60"><data value='init__'>BusinessLogicError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t73">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t73"><data value='init__'>DatabaseError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t91">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t91"><data value='init__'>AuthenticationError.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t102">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t102"><data value='init__'>AuthorizationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t120">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t120"><data value='init__'>NotFoundError.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t143">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t143"><data value='init__'>ConflictError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t161">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t161"><data value='init__'>ExternalServiceError.__init__</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t184">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t184"><data value='init__'>RateLimitError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t202">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t202"><data value='init__'>ConfigurationError.__init__</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t213">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t213"><data value='global_exception_handler'>global_exception_handler</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t264">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t264"><data value='create_error_response'>create_error_response</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>29</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="29 29">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t23">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t23"><data value='configure_logging'>configure_logging</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t37">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t37"><data value='configure_standard_logging'>configure_standard_logging</data></a></td>
                <td>21</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="15 21">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t85">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t85"><data value='configure_structlog'>configure_structlog</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t127">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t127"><data value='add_app_context'>add_app_context</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t153">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t153"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t175">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t175"><data value='logger'>LoggingMixin.logger</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t180">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t180"><data value='log_function_call'>log_function_call</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t193">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t193"><data value='log_api_request'>log_api_request</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t207">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t207"><data value='log_api_response'>log_api_response</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t230">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t230"><data value='log_database_operation'>log_database_operation</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t244">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t244"><data value='log_external_service_call'>log_external_service_call</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t258">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t258"><data value='log_business_event'>log_business_event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t271">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t271"><data value='log_security_event'>log_security_event</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t284">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t284"><data value='log_performance_metric'>log_performance_metric</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t29">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t29"><data value='dispatch'>ProcessTimeMiddleware.dispatch</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t62">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t62"><data value='dispatch'>LoggingMiddleware.dispatch</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t137">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t137"><data value='get_client_ip'>LoggingMiddleware._get_client_ip</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t168">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t168"><data value='init__'>RateLimitMiddleware.__init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t176">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t176"><data value='dispatch'>RateLimitMiddleware.dispatch</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t238">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t238"><data value='get_client_ip'>RateLimitMiddleware._get_client_ip</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t250">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t250"><data value='cleanup_expired_records'>RateLimitMiddleware._cleanup_expired_records</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t279">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t279"><data value='dispatch'>SecurityHeadersMiddleware.dispatch</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t315">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t315"><data value='dispatch'>CacheControlMiddleware.dispatch</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t50">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t50"><data value='lifespan'>lifespan</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t82">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t82"><data value='create_application'>create_application</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t114">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t114"><data value='configure_middleware'>configure_middleware</data></a></td>
                <td>9</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="7 9">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t147">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t147"><data value='configure_exception_handlers'>configure_exception_handlers</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t157">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t157"><data value='business_logic_exception_handler'>configure_exception_handlers.business_logic_exception_handler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t172">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t172"><data value='database_exception_handler'>configure_exception_handlers.database_exception_handler</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t185">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t185"><data value='validation_exception_handler'>configure_exception_handlers.validation_exception_handler</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t200">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t200"><data value='configure_routes'>configure_routes</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t210">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t210"><data value='health_check'>configure_routes.health_check</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t242">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html#t242"><data value='root'>configure_routes.root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="26 28">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t28">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t28"><data value='to_dict'>BaseModel.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t39">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t39"><data value='update_from_dict'>BaseModel.update_from_dict</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t50">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t50"><data value='repr__'>BaseModel.__repr__</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t113">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t113"><data value='is_deleted'>SoftDeleteMixin.is_deleted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t122">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t122"><data value='soft_delete'>SoftDeleteMixin.soft_delete</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t128">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t128"><data value='restore'>SoftDeleteMixin.restore</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t144">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t144"><data value='increment_version'>VersionMixin.increment_version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>699</td>
                <td>396</td>
                <td>0</td>
                <td class="right" data-ratio="303 699">43%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-03 22:36 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
