<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">43%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-03 22:36 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html">app/__init__.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html">app/api/__init__.py</a></td>
                <td class="name left"><a href="z_cfb6adc3f81c8e3c___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee___init___py.html">app/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_257b53c25398f6ee_api_py.html">app/api/v1/api.py</a></td>
                <td class="name left"><a href="z_257b53c25398f6ee_api_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d___init___py.html">app/api/v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html">app/api/v1/endpoints/health.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_health_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>60</td>
                <td>42</td>
                <td>0</td>
                <td class="right" data-ratio="18 60">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html">app/api/v1/endpoints/system.py</a></td>
                <td class="name left"><a href="z_41f09dac0431399d_system_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="22 56">39%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html">app/core/__init__.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t18">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t18"><data value='Settings'>Settings</data></a></td>
                <td>17</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="10 17">59%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t171">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html#t171"><data value='Config'>Settings.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html">app/core/config.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>69</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="69 69">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t26">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t26"><data value='Base'>Base</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t36">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html#t36"><data value='DatabaseManager'>DatabaseManager</data></a></td>
                <td>63</td>
                <td>60</td>
                <td>0</td>
                <td class="right" data-ratio="3 63">5%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html">app/core/database.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="26 54">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t16">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t16"><data value='BaseCustomException'>BaseCustomException</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t35">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t35"><data value='ValidationError'>ValidationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t53">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t53"><data value='BusinessLogicError'>BusinessLogicError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t66">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t66"><data value='DatabaseError'>DatabaseError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t84">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t84"><data value='AuthenticationError'>AuthenticationError</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t95">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t95"><data value='AuthorizationError'>AuthorizationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t113">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t113"><data value='NotFoundError'>NotFoundError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t136">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t136"><data value='ConflictError'>ConflictError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t154">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t154"><data value='ExternalServiceError'>ExternalServiceError</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t177">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t177"><data value='RateLimitError'>RateLimitError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t195">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html#t195"><data value='ConfigurationError'>ConfigurationError</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html">app/core/exceptions.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>40</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="29 40">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t167">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html#t167"><data value='LoggingMixin'>LoggingMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html">app/core/logging.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_logging_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>70</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="44 70">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t22">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t22"><data value='ProcessTimeMiddleware'>ProcessTimeMiddleware</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t55">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t55"><data value='LoggingMiddleware'>LoggingMiddleware</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t161">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t161"><data value='RateLimitMiddleware'>RateLimitMiddleware</data></a></td>
                <td>33</td>
                <td>33</td>
                <td>0</td>
                <td class="right" data-ratio="0 33">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t272">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t272"><data value='SecurityHeadersMiddleware'>SecurityHeadersMiddleware</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t308">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html#t308"><data value='CacheControlMiddleware'>CacheControlMiddleware</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html">app/core/middleware.py</a></td>
                <td class="name left"><a href="z_8f7e1016f2d37417_middleware_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>24</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="24 24">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html">app/main.py</a></td>
                <td class="name left"><a href="z_5f5a17c013354698_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>79</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="50 79">63%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html">app/models/__init__.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t16">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t16"><data value='BaseModel'>BaseModel</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t70">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t70"><data value='UUIDMixin'>UUIDMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t82">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t82"><data value='TimestampMixin'>TimestampMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t101">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t101"><data value='SoftDeleteMixin'>SoftDeleteMixin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t135">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t135"><data value='VersionMixin'>VersionMixin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t151">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html#t151"><data value='AuditMixin'>AuditMixin</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html">app/models/base.py</a></td>
                <td class="name left"><a href="z_6c0e4b930745278b_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>699</td>
                <td>396</td>
                <td>0</td>
                <td class="right" data-ratio="303 699">43%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-03 22:36 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
