{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "caece341e8ac740805522bc2c07cab3b", "files": {"z_5f5a17c013354698___init___py": {"hash": "b79c5d90888742bc46fd1d6b476ff6ed", "index": {"url": "z_5f5a17c013354698___init___py.html", "file": "app/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_cfb6adc3f81c8e3c___init___py": {"hash": "0d4954a4fcffc7c8a57443df56f8048c", "index": {"url": "z_cfb6adc3f81c8e3c___init___py.html", "file": "app/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_257b53c25398f6ee___init___py": {"hash": "607c58091fdedb69499296e7b460e7cc", "index": {"url": "z_257b53c25398f6ee___init___py.html", "file": "app/api/v1/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_257b53c25398f6ee_api_py": {"hash": "7c5f10cea76f62a7e3ba957656dee98e", "index": {"url": "z_257b53c25398f6ee_api_py.html", "file": "app/api/v1/api.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d___init___py": {"hash": "c74d0db30646e53b58bb961c49339563", "index": {"url": "z_41f09dac0431399d___init___py.html", "file": "app/api/v1/endpoints/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d_health_py": {"hash": "3a7d05b000ba971c0a255419fa604b3a", "index": {"url": "z_41f09dac0431399d_health_py.html", "file": "app/api/v1/endpoints/health.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 60, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_41f09dac0431399d_system_py": {"hash": "317d0fd455f9e56b2e903fe591742df0", "index": {"url": "z_41f09dac0431399d_system_py.html", "file": "app/api/v1/endpoints/system.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417___init___py": {"hash": "cc53b3781f4fd7f933ddc2e817f8b48d", "index": {"url": "z_8f7e1016f2d37417___init___py.html", "file": "app/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_config_py": {"hash": "0435f2174a8531ff3c3f1c56ab76906a", "index": {"url": "z_8f7e1016f2d37417_config_py.html", "file": "app/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_database_py": {"hash": "dca0e2144dd38b2941a71b7969762ec6", "index": {"url": "z_8f7e1016f2d37417_database_py.html", "file": "app/core/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 117, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_exceptions_py": {"hash": "e02efe6d835dbe2885838ddbf46f5bcb", "index": {"url": "z_8f7e1016f2d37417_exceptions_py.html", "file": "app/core/exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 49, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_logging_py": {"hash": "158b759e4338b11be7b3fdc9ca9d4348", "index": {"url": "z_8f7e1016f2d37417_logging_py.html", "file": "app/core/logging.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 27, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_8f7e1016f2d37417_middleware_py": {"hash": "c15f688b0a0cd29f6a3f43751b0011d8", "index": {"url": "z_8f7e1016f2d37417_middleware_py.html", "file": "app/core/middleware.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 100, "n_excluded": 0, "n_missing": 76, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5f5a17c013354698_main_py": {"hash": "6e3e32410a0a3e620893fa9b9deacf44", "index": {"url": "z_5f5a17c013354698_main_py.html", "file": "app/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 79, "n_excluded": 0, "n_missing": 29, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b___init___py": {"hash": "2daaf88db034f005ebc69db78d82947b", "index": {"url": "z_6c0e4b930745278b___init___py.html", "file": "app/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_6c0e4b930745278b_base_py": {"hash": "42cc2eae5b29a5fefa5c19420b06111d", "index": {"url": "z_6c0e4b930745278b_base_py.html", "file": "app/models/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 42, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}